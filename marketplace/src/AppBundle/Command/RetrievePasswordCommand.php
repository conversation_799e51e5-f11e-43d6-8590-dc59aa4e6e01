<?php

namespace AppBundle\Command;

use AppBundle\Entity\Merchant;
use AppBundle\Repository\MerchantRepository;
use Illuminate\Encryption\Encrypter;
use Open\LogBundle\Service\LogService;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class RetrievePasswordCommand extends Command
{
    private MerchantRepository $merchantRepository;
    private Encrypter $encryptor;
    private LogService $logger;

    public function __construct(
        MerchantRepository $merchantRepository,
        Encrypter $encryptor,
        LogService $logger,
        string $name = null
    ) {
        parent::__construct($name);
        $this->merchantRepository = $merchantRepository;
        $this->encryptor = $encryptor;
        $this->logger = $logger;
    }

    protected function configure()
    {
        $this
            ->setName('retrieve-password')
            ->setDescription('Retrieve merchant password')
            ->addArgument('email', InputArgument::REQUIRED, 'Email of the merchant');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $email = $input->getArgument('email');
        $merchant = $this->merchantRepository->findOneBy(['email' => $email]);

        if (!$merchant) {
            $output->writeln('<error>Merchant not found for email: ' . $email . '</error>');
            $this->logger->error('Merchant not found for email: ' . $email);
            return Command::FAILURE;
        }

        $decryptedPassword = $this->encryptor->decrypt($merchant->getPassword());
        $output->writeln('Password: ' . $decryptedPassword);
        return Command::SUCCESS;
    }
}
